from __future__ import annotations

import pandas as pd

from .config import Settings, ensure_dirs
from .alpaca_client import AlpacaClient
from .features import add_features
from .strategy import generate_signals


def run():
    cfg = Settings()
    ensure_dirs(cfg)
    client = AlpacaClient(cfg)

    acct = client.get_account()
    print("Account equity:", acct.equity, "cash:", acct.cash)

    clk = client.get_clock()
    print("Market is_open:", getattr(clk, 'is_open', None))

    sym = list(cfg.symbols)[0]
    print(f"Fetching recent minute bars for {sym}...")
    df = client.get_bars(sym, timeframe=cfg.bar_timeframe, limit=100)
    print(f"Bars fetched: {len(df)}")
    if not df.empty:
        feats = add_features(df)
        sigs = generate_signals(feats)
        print("Sample signals (top 3 by score abs):")
        for s in sigs[:3]:
            print(s)

    print("Positions:")
    poss = client.list_positions()
    for p in poss:
        print(p.symbol, p.qty)


if __name__ == "__main__":
    run()

