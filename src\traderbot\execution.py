from __future__ import annotations

from dataclasses import dataclass
from typing import Dict

from .alpaca_client import AlpacaClient, OrderSpec
from .config import Settings


@dataclass
class AccountSnapshot:
    equity: float
    cash: float


def position_size_from_risk(account: AccountSnapshot, price: float, risk_pct: float, max_pos_value_pct: float) -> int:
    # Risk-based sizing with a cap on position notional value
    risk_dollars = account.equity * risk_pct
    stop_pct = 0.002
    denom = max(price * stop_pct, 1e-6)
    qty_by_risk = int(risk_dollars / denom)

    max_value = account.equity * max_pos_value_pct
    qty_by_cap = int(max_value / max(price, 1e-6))
    qty = max(min(qty_by_risk, qty_by_cap), 1)
    return qty


def execute_signals(client: AlpacaClient, cfg: Settings, signals, current_positions: Dict[str, int]):
    acct = client.get_account()
    account = AccountSnapshot(equity=float(acct.equity), cash=float(acct.cash))

    # buy top signals not already held and above min threshold
    buys = [s for s in signals if s.direction > 0 and abs(s.score) >= cfg.signal_abs_min and s.symbol not in current_positions]
    for sig in buys[: cfg.max_concurrent_positions]:
        last = client.get_last_quote(sig.symbol)
        if last is None:
            continue
        # skip if spread too wide
        spread = client.get_spread_bbo(sig.symbol)
        if spread is not None and spread > cfg.max_spread_pct:
            continue
        # maintain cash buffer
        if account.cash <= account.equity * cfg.min_cash_buffer_pct:
            break
        qty = position_size_from_risk(account, last, cfg.max_position_risk_pct, cfg.max_pos_value_pct)
        if qty <= 0:
            continue
        spec = OrderSpec(symbol=sig.symbol, qty=qty, side='buy', take_profit_pct=cfg.take_profit_pct, stop_loss_pct=cfg.stop_loss_pct)
        try:
            client.submit_bracket_order(spec)
        except Exception:
            continue

