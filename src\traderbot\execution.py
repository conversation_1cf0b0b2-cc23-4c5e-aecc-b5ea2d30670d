from __future__ import annotations

from dataclasses import dataclass
from typing import Dict

from .alpaca_client import AlpacaClient, OrderSpec
from .config import Settings


@dataclass
class AccountSnapshot:
    equity: float
    cash: float


def position_size_from_risk(account: AccountSnapshot, price: float, risk_pct: float) -> int:
    # Risk percent dictates how much of equity is risked per trade via stop
    risk_dollars = account.equity * risk_pct
    # If stop is roughly 0.2% (configurable), shares approx risk / (price * stop_pct)
    # Using simple assumption that stop_loss_pct ~ 0.2%
    stop_pct = 0.002
    denom = max(price * stop_pct, 1e-6)
    qty = int(risk_dollars / denom)
    return max(qty, 1)


def execute_signals(client: AlpacaClient, cfg: Settings, signals, current_positions: Dict[str, int]):
    acct = client.get_account()
    account = AccountSnapshot(equity=float(acct.equity), cash=float(acct.cash))

    # buy top signals not already held
    buys = [s for s in signals if s.direction > 0 and s.symbol not in current_positions]
    for sig in buys[: cfg.max_concurrent_positions]:
        last = client.get_last_quote(sig.symbol)
        if last is None:
            continue
        if account.cash <= account.equity * cfg.min_cash_buffer_pct:
            break
        qty = position_size_from_risk(account, last, cfg.max_position_risk_pct)
        spec = OrderSpec(symbol=sig.symbol, qty=qty, side='buy', take_profit_pct=cfg.take_profit_pct, stop_loss_pct=cfg.stop_loss_pct)
        try:
            client.submit_bracket_order(spec)
        except Exception:
            continue

