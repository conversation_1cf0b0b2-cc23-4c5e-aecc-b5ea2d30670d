from __future__ import annotations

import os
import pickle
from datetime import datetime

import pandas as pd
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.pipeline import Pipeline

from .config import Settings
from .data_store import daily_path
from .features import add_features, make_labels


FEATURE_COLS = [
    'ret_1', 'ret_3', 'ret_5', 'vol_chg', 'vwap_gap', 'rsi_14', 'ema_diff'
]


def train_end_of_day(cfg: Settings, date: datetime) -> str | None:
    # Load aggregated daily minute data and train model
    bars_path = daily_path(cfg.data_dir, date, 'minute_bars')
    if not os.path.exists(bars_path):
        return None
    df = pd.read_parquet(bars_path)
    df = add_features(df)
    y = make_labels(df)
    X = df[FEATURE_COLS].fillna(0.0)

    # simple model pipeline
    pipe = Pipeline([
        ('scaler', StandardScaler()),
        ('rf', RandomForestClassifier(n_estimators=200, max_depth=8, random_state=42))
    ])
    pipe.fit(X, y)

    os.makedirs(cfg.models_dir, exist_ok=True)
    model_path = os.path.join(cfg.models_dir, 'model.pkl')
    with open(model_path, 'wb') as f:
        pickle.dump(pipe, f)
    return model_path

