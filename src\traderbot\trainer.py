from __future__ import annotations

import os
import pickle
import time
from datetime import datetime
from typing import List

import pandas as pd
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.pipeline import Pipeline

from .config import Settings
from .features import add_features, make_labels


FEATURE_COLS = [
    'ret_1', 'ret_3', 'ret_5', 'vol_chg', 'vwap_gap', 'rsi_14', 'ema_diff'
]


def _list_all_daily_bars_files(data_dir: str) -> List[str]:
    files: List[str] = []
    if not os.path.isdir(data_dir):
        return files
    for entry in os.listdir(data_dir):
        day_dir = os.path.join(data_dir, entry)
        if not os.path.isdir(day_dir):
            continue
        fpath = os.path.join(day_dir, 'minute_bars.parquet')
        if os.path.exists(fpath):
            files.append(fpath)
    files.sort()
    return files


def train_end_of_day(cfg: Settings, date: datetime) -> str | None:
    """
    Train on ALL collected data across all days, not just today.
    Aggregates every data/YYYY-MM-DD/minute_bars.parquet, engineers features,
    builds labels, fits a model, and saves it to models/model.pkl
    """
    t0 = time.time()
    files = _list_all_daily_bars_files(cfg.data_dir)
    if not files:
        print("[Trainer] No data files found to train on.")
        return None

    print(f"[Trainer] Aggregating {len(files)} day file(s).")

    dfs = []
    total_rows = 0
    for f in files:
        try:
            df_part = pd.read_parquet(f)
            total_rows += len(df_part)
            dfs.append(df_part)
        except Exception as e:
            print(f"[Trainer] Skipping unreadable file: {f} ({e})")
            continue
    if not dfs:
        print("[Trainer] No readable data to train on.")
        return None

    df = pd.concat(dfs, ignore_index=True)
    if df.empty:
        print("[Trainer] Aggregated dataframe is empty.")
        return None

    # Ensure proper dtypes
    df['time'] = pd.to_datetime(df['time'], errors='coerce')

    # Basic coverage stats
    n_syms = df['symbol'].nunique(dropna=True)
    tmin = df['time'].min()
    tmax = df['time'].max()

    # Deduplicate and sort for stable feature/label generation
    n_before = len(df)
    df = df.drop_duplicates().sort_values(['symbol', 'time']).reset_index(drop=True)
    n_after = len(df)

    print(f"[Trainer] Rows loaded: {total_rows}, after concat: {n_before}, after dedup: {n_after}")
    print(f"[Trainer] Symbols: {n_syms}, time span: {tmin} -> {tmax}")

    df = add_features(df)
    y = make_labels(df)
    X = df[FEATURE_COLS].fillna(0.0)

    # Label distribution
    vc = y.value_counts().to_dict()
    print(f"[Trainer] Training samples: {len(X)}; label distribution: {vc}")

    # simple model pipeline
    pipe = Pipeline([
        ('scaler', StandardScaler()),
        ('rf', RandomForestClassifier(n_estimators=200, max_depth=8, random_state=42))
    ])
    pipe.fit(X, y)

    os.makedirs(cfg.models_dir, exist_ok=True)
    model_path = os.path.join(cfg.models_dir, 'model.pkl')
    with open(model_path, 'wb') as f:
        pickle.dump(pipe, f)

    elapsed = time.time() - t0
    print(f"[Trainer] Model saved to {model_path}. Training time: {elapsed:.2f}s")
    return model_path

