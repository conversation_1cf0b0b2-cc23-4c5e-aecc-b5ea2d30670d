from __future__ import annotations

import os
import pickle
from datetime import datetime
from typing import List

import pandas as pd
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.pipeline import Pipeline

from .config import Settings
from .features import add_features, make_labels


FEATURE_COLS = [
    'ret_1', 'ret_3', 'ret_5', 'vol_chg', 'vwap_gap', 'rsi_14', 'ema_diff'
]


def _list_all_daily_bars_files(data_dir: str) -> List[str]:
    files: List[str] = []
    if not os.path.isdir(data_dir):
        return files
    for entry in os.listdir(data_dir):
        day_dir = os.path.join(data_dir, entry)
        if not os.path.isdir(day_dir):
            continue
        fpath = os.path.join(day_dir, 'minute_bars.parquet')
        if os.path.exists(fpath):
            files.append(fpath)
    files.sort()
    return files


def train_end_of_day(cfg: Settings, date: datetime) -> str | None:
    """
    Train on ALL collected data across all days, not just today.
    Aggregates every data/YYYY-MM-DD/minute_bars.parquet, engineers features,
    builds labels, fits a model, and saves it to models/model.pkl
    """
    files = _list_all_daily_bars_files(cfg.data_dir)
    if not files:
        return None

    dfs = []
    for f in files:
        try:
            dfs.append(pd.read_parquet(f))
        except Exception:
            continue
    if not dfs:
        return None

    df = pd.concat(dfs, ignore_index=True)
    if df.empty:
        return None

    # Deduplicate and sort for stable feature/label generation
    df = df.drop_duplicates().sort_values(['symbol', 'time']).reset_index(drop=True)

    df = add_features(df)
    y = make_labels(df)
    X = df[FEATURE_COLS].fillna(0.0)

    # simple model pipeline
    pipe = Pipeline([
        ('scaler', StandardScaler()),
        ('rf', RandomForestClassifier(n_estimators=200, max_depth=8, random_state=42))
    ])
    pipe.fit(X, y)

    os.makedirs(cfg.models_dir, exist_ok=True)
    model_path = os.path.join(cfg.models_dir, 'model.pkl')
    with open(model_path, 'wb') as f:
        pickle.dump(pipe, f)
    return model_path

