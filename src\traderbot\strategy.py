from __future__ import annotations

from dataclasses import dataclass
import pandas as pd
import math


@dataclass
class Signal:
    symbol: str
    score: float
    direction: int  # 1 buy, -1 sell, 0 hold


def _safe(v: float, default: float = 0.0) -> float:
    try:
        f = float(v)
        if math.isfinite(f):
            return f
        return default
    except Exception:
        return default


def generate_signals(feat_df: pd.DataFrame, ml_scored: pd.DataFrame | None = None, ml_weight: float = 0.5) -> list[Signal]:
    # Momentum/mean-reversion blend with optional ML score fusion
    signals: list[Signal] = []
    if feat_df is None or feat_df.empty:
        return signals

    latest = feat_df.sort_values('time').groupby('symbol').tail(1).set_index('symbol')

    # merge with ml_scored if provided
    if ml_scored is not None and not ml_scored.empty and 'ml_score' in ml_scored.columns:
        ml_latest = ml_scored.sort_values('time').groupby('symbol').tail(1).set_index('symbol')
        latest = latest.join(ml_latest[['ml_score']], how='left', rsuffix='_ml')

    for sym, row in latest.iterrows():
        score = 0.0
        # Momentum
        score += 1.5 * _safe(row.get('ret_3', 0))
        score += 1.0 * _safe(row.get('ema_diff', 0))
        # Mean reversion around VWAP
        score += -0.5 * _safe(row.get('vwap_gap', 0))
        # RSI extremes
        rsi = _safe(row.get('rsi_14', 50), 50.0)
        if rsi < 30:
            score += 0.003
        elif rsi > 70:
            score -= 0.003
        # ML score fusion (shift to [-0.5, 0.5] if prob)
        ml_score = row.get('ml_score')
        if pd.notna(ml_score):
            ml_c = _safe(ml_score, 0.5)
            if 0 <= ml_c <= 1:
                ml_c = ml_c - 0.5
            score = (1 - ml_weight) * score + ml_weight * ml_c

        direction = 1 if score > 0.0005 else (-1 if score < -0.0005 else 0)
        signals.append(Signal(symbol=sym, score=score, direction=direction))

    signals.sort(key=lambda s: abs(s.score), reverse=True)
    return signals

