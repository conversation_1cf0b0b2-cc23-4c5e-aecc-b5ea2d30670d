from __future__ import annotations

import pandas as pd
import numpy as np


def add_features(df: pd.DataFrame) -> pd.DataFrame:
    # expects columns: time, open, high, low, close, volume, symbol
    out = df.copy()
    out = out.sort_values(['symbol', 'time']).reset_index(drop=True)

    def group_apply(g: pd.DataFrame) -> pd.DataFrame:
        g = g.copy()
        g['ret_1'] = g['close'].pct_change(1)
        g['ret_3'] = g['close'].pct_change(3)
        g['ret_5'] = g['close'].pct_change(5)
        g['vol_chg'] = g['volume'].pct_change(1)
        g['vwap'] = (g['close'] * g['volume']).rolling(10).sum() / (g['volume'].rolling(10).sum())
        g['vwap_gap'] = (g['close'] - g['vwap']) / g['vwap']
        g['rsi_14'] = rsi(g['close'], 14)
        g['ema_fast'] = g['close'].ewm(span=8, adjust=False).mean()
        g['ema_slow'] = g['close'].ewm(span=21, adjust=False).mean()
        g['ema_diff'] = (g['ema_fast'] - g['ema_slow']) / g['ema_slow']
        return g

    out = out.groupby('symbol', group_keys=False).apply(group_apply)
    return out


def rsi(close: pd.Series, period: int = 14) -> pd.Series:
    delta = close.diff()
    gain = (delta.where(delta > 0, 0)).rolling(period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(period).mean()
    rs = gain / (loss + 1e-9)
    return 100 - (100 / (1 + rs))


def make_labels(df: pd.DataFrame, horizon: int = 3, thresh: float = 0.002) -> pd.Series:
    # Label +1 if next N bars increases >= thresh, -1 if decreases <= -thresh, else 0
    future = df.groupby('symbol')['close'].shift(-horizon)
    ret = (future - df['close']) / df['close']
    y = pd.Series(0, index=df.index)
    y[ret >= thresh] = 1
    y[ret <= -thresh] = -1
    return y

