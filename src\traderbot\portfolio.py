from __future__ import annotations

from dataclasses import dataclass
from typing import Dict, List


@dataclass
class Position:
    symbol: str
    qty: int


class PortfolioPolicy:
    def __init__(self, min_distinct: int = 3, max_concurrent: int = 5):
        self.min_distinct = min_distinct
        self.max_concurrent = max_concurrent

    def decide_targets(self, signals: List, current_positions: Dict[str, Position]) -> List[str]:
        # Returns list of symbols to prioritize entering to maintain diversity and follow signals
        desired = []
        have = set(current_positions.keys())
        # prioritize new symbols not in portfolio with positive signals
        for s in signals:
            if s.direction > 0 and s.symbol not in have and len(desired) + len(have) < self.max_concurrent:
                desired.append(s.symbol)
        # If below min_distinct, force add additional with weakest positive signals
        if len(have) + len(desired) < self.min_distinct:
            for s in signals[::-1]:  # from weakest to strongest
                if s.direction > 0 and s.symbol not in have and s.symbol not in desired:
                    desired.append(s.symbol)
                    if len(have) + len(desired) >= self.min_distinct:
                        break
        return desired

