from __future__ import annotations

import datetime as dt
import time
import os
import pytz
import pandas as pd

from .config import Settings, ensure_dirs
from .alpaca_client import AlpacaClient
from .features import add_features
from .strategy import generate_signals
from .portfolio import PortfolioPolicy
from .execution import execute_signals
from .data_store import daily_path, append_parquet
from .trainer import train_end_of_day
from .model_scoring import maybe_load_model, score_with_model


def is_market_open(client: AlpacaClient) -> bool:
    clk = client.get_clock()
    return bool(clk.is_open)


def now_ny(cfg: Settings) -> dt.datetime:
    tz = pytz.timezone(cfg.market_tz)
    return dt.datetime.now(tz)


def collect_and_trade_loop():
    cfg = Settings()
    ensure_dirs(cfg)
    client = AlpacaClient(cfg)
    policy = PortfolioPolicy(min_distinct=3, max_concurrent=cfg.max_concurrent_positions)

    print("TraderBot starting with symbols:", cfg.symbols)

    # main loop during market hours
    last_train_date = None
    while True:
        clk_open = is_market_open(client)
        tnow = now_ny(cfg)
        if clk_open:
            # Collect latest minute bars for all symbols
            start = tnow - dt.timedelta(minutes=30)
            end = tnow
            dfs = []
            for sym in cfg.symbols:
                try:
                    df = client.get_bars(sym, start, end, timeframe=cfg.bar_timeframe)
                except Exception:
                    df = None
                if df is not None and not df.empty:
                    dfs.append(df)
            if dfs:
                bars = pd.concat(dfs).sort_values(['symbol', 'time']).reset_index(drop=True)
                # Persist raw minute bars for the day
                append_parquet(daily_path(cfg.data_dir, tnow, 'minute_bars'), bars)

                feats = add_features(bars)
                # ML scoring fusion
                model = maybe_load_model(cfg.models_dir)
                ml_scored = score_with_model(model, bars) if model is not None else None
                signals = generate_signals(feats, ml_scored=ml_scored, ml_weight=0.5)

                # current positions
                pos = {p.symbol: int(p.qty) for p in client.list_positions()}

                # ensure portfolio diversity
                desired_new = policy.decide_targets(signals, pos)
                # modify signals to only include desired_new buys
                filtered = [s for s in signals if s.symbol in desired_new]
                execute_signals(client, cfg, filtered, pos)

            time.sleep(30)  # throttle to ~2 per minute
        else:
            # market closed -> end of day training once per day
            if last_train_date != tnow.date():
                print("Market closed; training model for", tnow.date())
                model_path = train_end_of_day(cfg, tnow)
                if model_path:
                    print("Saved model:", model_path)
                last_train_date = tnow.date()
            time.sleep(60)


if __name__ == "__main__":
    collect_and_trade_loop()

