from __future__ import annotations

import os
import pickle
import pandas as pd

from .features import add_features


def maybe_load_model(models_dir: str):
    path = os.path.join(models_dir, 'model.pkl')
    if os.path.exists(path):
        with open(path, 'rb') as f:
            return pickle.load(f)
    return None


def score_with_model(model, bars: pd.DataFrame) -> pd.DataFrame:
    if model is None or bars is None or bars.empty:
        return pd.DataFrame()
    feat = add_features(bars)
    cols = ['ret_1', 'ret_3', 'ret_5', 'vol_chg', 'vwap_gap', 'rsi_14', 'ema_diff']
    X = feat[cols].fillna(0.0)
    # predict_proba may not exist; fall back to decision_function or predict
    if hasattr(model, 'predict_proba'):
        proba = model.predict_proba(X)
        # assume classes are [-1,0,1] or [0,1]; take probability of positive move
        if proba.shape[1] == 3:
            # class 2 corresponds to 1 if trained that way; we will map by class order
            classes = getattr(model, 'classes_', None)
            pos_idx = list(classes).index(1) if classes is not None and 1 in classes else proba.shape[1]-1
            score = proba[:, pos_idx]
        else:
            score = proba[:, -1]
    elif hasattr(model, 'decision_function'):
        score = model.decision_function(X)
    else:
        score = model.predict(X)
    feat = feat.copy()
    feat['ml_score'] = score
    return feat

