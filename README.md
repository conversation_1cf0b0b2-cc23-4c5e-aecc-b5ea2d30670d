## Overview

This project is a modular Python trading system for Alpaca paper trading focused on quick, small-profit trades (scalps). It:

- Connects to Alpaca paper trading using API keys from a .env file
- Trades during market hours and targets frequent, small wins with bracket orders
- Ensures the portfolio maintains at least 3 distinct symbols when possible
- Collects minute-level data and engineered features into a local dataset
- Trains an end-of-day machine learning model to iteratively improve decisions

## Features

- Strategy: momentum/scalp-oriented signals with risk controls
- Portfolio policy: maintain >= 3 distinct positions when signals permit
- Data: minute bars + engineered features stored as Parquet/CSV for ML
- ML: scikit-learn pipeline trained daily; model persisted to models/model.pkl

## Project structure

- requirements.txt – Python dependencies
- .env.example – Example environment variables
- src/traderbot/
  - config.py – Env loading and constants
  - alpaca_client.py – Thin wrapper around Alpaca REST for trading + data
  - features.py – Feature engineering and label generation
  - data_store.py – Persistence helpers (CSV/Parquet)
  - strategy.py – Signal generation logic
  - portfolio.py – Rules to keep >= 3 symbols in positions
  - execution.py – Order sizing + bracket order submission
  - trainer.py – End-of-day model training
  - main.py – Orchestration loop

## Setup

1) Create and populate .env (see .env.example)
2) Create a virtualenv and install deps

- Windows (PowerShell):
  - python -m venv .venv
  - .venv\\Scripts\\Activate.ps1
  - pip install -r requirements.txt

3) Run

- python -m src.traderbot.main

## Notes

- Uses free IEX market data feed where applicable
- Paper trading only; review code before real-money use
- Consider limiting order frequency to respect rate limits

## Testing

- Tests (suggested): pytest
- Example command: pytest -q
- Add unit tests for features, labeling, and position sizing

