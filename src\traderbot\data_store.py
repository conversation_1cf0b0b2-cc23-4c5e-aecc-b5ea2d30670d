from __future__ import annotations

import os
import pandas as pd
from datetime import datetime


def daily_path(data_dir: str, date: datetime, kind: str, ext: str = 'parquet') -> str:
    ymd = date.strftime('%Y-%m-%d')
    os.makedirs(os.path.join(data_dir, ymd), exist_ok=True)
    return os.path.join(data_dir, ymd, f"{kind}.{ext}")


def append_parquet(path: str, df: pd.DataFrame) -> None:
    if df is None or df.empty:
        return
    if os.path.exists(path):
        existing = pd.read_parquet(path)
        combined = pd.concat([existing, df]).drop_duplicates().reset_index(drop=True)
        combined.to_parquet(path, index=False)
    else:
        df.to_parquet(path, index=False)


def save_csv(path: str, df: pd.DataFrame) -> None:
    if df is None or df.empty:
        return
    df.to_csv(path, index=False)

