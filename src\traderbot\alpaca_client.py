from __future__ import annotations

import datetime as dt
from dataclasses import dataclass
from typing import Iterable, Optional

import pandas as pd
from alpaca_trade_api import REST
from alpaca_trade_api.rest import APIError

from .config import Settings


@dataclass
class OrderSpec:
    symbol: str
    qty: int
    side: str  # 'buy' | 'sell'
    take_profit_pct: float
    stop_loss_pct: float


class AlpacaClient:
    def __init__(self, cfg: Settings):
        self.cfg = cfg
        self.api = REST(key_id=cfg.api_key, secret_key=cfg.api_secret, base_url=cfg.base_url)

    def get_account(self):
        return self.api.get_account()

    def list_positions(self) -> list:
        return self.api.list_positions()

    def get_clock(self):
        return self.api.get_clock()

    def list_assets(self, symbols: Iterable[str]) -> dict[str, any]:
        assets = {}
        for s in symbols:
            assets[s] = self.api.get_asset(s)
        return assets

    def get_last_quote(self, symbol: str) -> Optional[float]:
        try:
            q = self.api.get_last_quote(symbol)
            # some feeds use askprice/bidprice
            px = getattr(q, 'last', None)
            if px and hasattr(px, 'askprice'):
                return float(px.askprice)
            if hasattr(q, 'ask') and q.ask:
                return float(q.ask)
        except Exception:
            return None
        return None

    def get_bars(self, symbol: str, start: dt.datetime, end: dt.datetime, timeframe: str = "1Min") -> pd.DataFrame:
        # Using get_bars with proper timeframe string
        bars = self.api.get_bars(symbol, timeframe, start.isoformat(), end.isoformat(), adjustment='raw')
        if hasattr(bars, 'df'):
            df = bars.df
            df = df.reset_index()
            df = df.rename(columns={'timestamp': 'time'})
            df['time'] = pd.to_datetime(df['time'])
            df['symbol'] = symbol
            return df
        return pd.DataFrame()

    def submit_bracket_order(self, spec: OrderSpec, limit_price: Optional[float] = None):
        try:
            params = dict(
                symbol=spec.symbol,
                qty=spec.qty,
                side=spec.side,
                type='limit' if limit_price else 'market',
                time_in_force='day',
                order_class='bracket',
                take_profit={'limit_price': None},
                stop_loss={'stop_price': None},
            )
            if limit_price:
                params['limit_price'] = limit_price
            # For brackets, specify prices relative to current price is not supported; we compute absolute
            last = self.get_last_quote(spec.symbol)
            if last is None:
                raise RuntimeError(f"No quote for {spec.symbol}")
            if spec.side == 'buy':
                tp = last * (1 + spec.take_profit_pct)
                sl = last * (1 - spec.stop_loss_pct)
            else:
                tp = last * (1 - spec.take_profit_pct)
                sl = last * (1 + spec.stop_loss_pct)
            params['take_profit']['limit_price'] = round(tp, 2)
            params['stop_loss']['stop_price'] = round(sl, 2)
            return self.api.submit_order(**params)
        except APIError as e:
            raise e

    def cancel_all(self):
        return self.api.cancel_all_orders()

    def list_open_orders(self):
        return self.api.list_orders(status='open', direction='asc')

