from __future__ import annotations

import datetime as dt
from dataclasses import dataclass
from typing import Iterable, Optional

import pandas as pd
from alpaca_trade_api import REST
from alpaca_trade_api.rest import APIError

from .config import Settings


@dataclass
class OrderSpec:
    symbol: str
    qty: int
    side: str  # 'buy' | 'sell'
    take_profit_pct: float
    stop_loss_pct: float


class AlpacaClient:
    def __init__(self, cfg: Settings):
        self.cfg = cfg
        self.api = REST(key_id=cfg.api_key, secret_key=cfg.api_secret, base_url=cfg.base_url)

    def get_account(self):
        return self.api.get_account()

    def list_positions(self) -> list:
        return self.api.list_positions()

    def get_clock(self):
        return self.api.get_clock()

    def list_assets(self, symbols: Iterable[str]) -> dict[str, any]:
        assets = {}
        for s in symbols:
            assets[s] = self.api.get_asset(s)
        return assets

    def get_last_quote(self, symbol: str) -> Optional[float]:
        # Try multiple endpoints/fields and prefer ask price for buys
        try:
            try:
                q = self.api.get_latest_quote(symbol)
                ask = getattr(q, 'ask_price', None) or getattr(q, 'AskPrice', None)
                bid = getattr(q, 'bid_price', None) or getattr(q, 'BidPrice', None)
                if ask is not None:
                    return float(ask)
                if bid is not None:
                    return float(bid)
            except Exception:
                pass
            # fallback
            q = self.api.get_last_quote(symbol)
            px = getattr(q, 'askprice', None) or getattr(q, 'ask', None)
            if px:
                return float(px)
        except Exception:
            return None
        return None

    def get_spread_bbo(self, symbol: str) -> Optional[float]:
        try:
            q = self.api.get_latest_quote(symbol)
            ask = getattr(q, 'ask_price', None)
            bid = getattr(q, 'bid_price', None)
            if ask is not None and bid is not None and ask > 0 and bid > 0:
                return float((ask - bid) / ask)
        except Exception:
            pass
        return None

    def get_bars(self, symbol: str, start: dt.datetime | None = None, end: dt.datetime | None = None, timeframe: str = "1Min", limit: int = 300) -> pd.DataFrame:
        # Use limit-based fetch and free IEX feed for paper entitlements
        try:
            start_str = None
            end_str = None
            if start is not None:
                if start.tzinfo is None:
                    start = start.replace(tzinfo=dt.timezone.utc)
                start_str = start.isoformat().replace("+00:00", "Z")
            if end is not None:
                if end.tzinfo is None:
                    end = end.replace(tzinfo=dt.timezone.utc)
                end_str = end.isoformat().replace("+00:00", "Z")

            bars = self.api.get_bars(symbol, timeframe, start_str, end_str, adjustment='raw', limit=limit, feed='iex')
            if hasattr(bars, 'df'):
                df = bars.df
                df = df.reset_index()
                df = df.rename(columns={'timestamp': 'time'})
                df['time'] = pd.to_datetime(df['time'])
                df['symbol'] = symbol
                return df
        except Exception:
            pass
        return pd.DataFrame()

    def submit_bracket_order(self, spec: OrderSpec, limit_price: Optional[float] = None):
        try:
            params = dict(
                symbol=spec.symbol,
                qty=spec.qty,
                side=spec.side,
                type='limit' if limit_price else 'market',
                time_in_force='day',
                order_class='bracket',
                take_profit={'limit_price': None},
                stop_loss={'stop_price': None},
            )
            if limit_price:
                params['limit_price'] = limit_price
            last = self.get_last_quote(spec.symbol)
            if last is None:
                raise RuntimeError(f"No quote for {spec.symbol}")
            if spec.side == 'buy':
                tp = last * (1 + spec.take_profit_pct)
                sl = last * (1 - spec.stop_loss_pct)
            else:
                tp = last * (1 - spec.take_profit_pct)
                sl = last * (1 + spec.stop_loss_pct)
            params['take_profit']['limit_price'] = round(tp, 2)
            params['stop_loss']['stop_price'] = round(sl, 2)
            return self.api.submit_order(**params)
        except APIError as e:
            raise e

    def cancel_all(self):
        return self.api.cancel_all_orders()

    def list_open_orders(self):
        return self.api.list_orders(status='open', direction='asc')

