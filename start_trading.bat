@echo off
setlocal enabledelayedexpansion

rem Change directory to the repo root (this script's directory)
pushd "%~dp0"

rem Prefer the project virtualenv's python if it exists; otherwise fall back to system python
set "PY_EXE=python"
if exist ".venv\Scripts\python.exe" set "PY_EXE=.venv\Scripts\python.exe"

echo Using Python: %PY_EXE%
echo Starting TraderBot live trading loop...

%PY_EXE% -m src.traderbot.main
set EXITCODE=%ERRORLEVEL%

echo TraderBot exited with code %EXITCODE%
popd
exit /b %EXITCODE%

