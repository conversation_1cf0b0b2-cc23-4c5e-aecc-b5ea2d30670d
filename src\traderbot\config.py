import os
from dataclasses import dataclass
from dotenv import load_dotenv

load_dotenv()

@dataclass(frozen=True)
class Settings:
    api_key: str = os.getenv("ALPACA_API_KEY_ID", "")
    api_secret: str = os.getenv("ALPACA_API_SECRET_KEY", "")
    base_url: str = os.getenv("ALPACA_BASE_URL", "https://paper-api.alpaca.markets")
    data_dir: str = os.getenv("DATA_DIR", "data")
    models_dir: str = os.getenv("MODELS_DIR", "models")
    symbols: list[str] = tuple(os.getenv("SYMBOLS", "SPY,AAPL,MSFT").split(","))
    max_concurrent_positions: int = int(os.getenv("MAX_CONCURRENT_POSITIONS", "5"))
    max_position_risk_pct: float = float(os.getenv("MAX_POSITION_RISK_PCT", "0.05"))
    take_profit_pct: float = float(os.getenv("TAKE_PROFIT_PCT", "0.003"))
    stop_loss_pct: float = float(os.getenv("STOP_LOSS_PCT", "0.002"))
    min_cash_buffer_pct: float = float(os.getenv("MIN_CASH_BUFFER_PCT", "0.1"))
    bar_timeframe: str = os.getenv("BAR_TIMEFRAME", "1Min")
    market_tz: str = os.getenv("MARKET_TIMEZONE", "America/New_York")


def ensure_dirs(cfg: Settings) -> None:
    os.makedirs(cfg.data_dir, exist_ok=True)
    os.makedirs(cfg.models_dir, exist_ok=True)

